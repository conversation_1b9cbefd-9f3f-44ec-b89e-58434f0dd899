# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

HiveChatDoc is a documentation website for HiveChat, an AI chat application designed for small to medium teams. It's built with Next.js and Fumadocs, featuring multilingual support (Chinese and English) and comprehensive documentation for AI model providers, authentication methods, and installation guides.

## Development Commands

### Core Commands
- `npm run dev` or `pnpm dev` - Start development server with Turbo (runs on http://localhost:3000)
- `npm run build` - Build production version
- `npm start` - Start production server
- `npm run postinstall` - Generate MDX content (runs automatically after install)

### Package Manager
The project uses pnpm (evidenced by pnpm-lock.yaml), though npm also works.

## Architecture

### Core Framework
- **Next.js 15.3.1** with App Router architecture
- **Fumadocs** for documentation site generation
- **TypeScript** with strict configuration
- **Tailwind CSS 4.x** for styling

### Internationalization (i18n)
- Default language: `zh` (Chinese)
- Supported languages: `zh`, `en`
- URL structure: `/[lang]/...` with middleware-based routing
- Translations configured in `app/[lang]/layout.tsx`

### Directory Structure

#### App Structure (`app/`)
- `[lang]/` - Internationalized routes
  - `(home)/` - Homepage components
  - `docs/[[...slug]]/` - Dynamic documentation routes
  - `layout.tsx` - Language-specific layout
- `api/search/` - Search API endpoint
- `layout.config.tsx` - Shared layout configuration

#### Content Management (`content/docs/`)
- `auth/` - Authentication documentation (DingDing, Email, Feishu, WeCom)
- `install/` - Installation guides (Docker, Local, Vercel)
- `providers/` - AI provider documentation (Claude, OpenAI, Gemini, etc.)
- `meta.json` files control navigation structure
- All content in `.mdx` format

#### Components (`components/`)
- `AmplitudeProvider.tsx` - Analytics integration
- `LanguageSwitcher.tsx` - Language switching UI
- `home/` - Homepage-specific components (HomeCN, HomeEN)
- `shared/` - Reusable components and types

#### Library Code (`lib/`)
- `i18n.ts` - Internationalization configuration
- `source.ts` - Fumadocs source configuration
- `amplitude.ts` - Analytics configuration

### Key Configuration Files
- `source.config.ts` - Fumadocs MDX configuration
- `middleware.ts` - i18n routing middleware
- `tsconfig.json` - TypeScript with path aliases (`@/*`)
- `next.config.mjs` - Next.js with Fumadocs MDX integration

### Content Guidelines
- Documentation is bilingual (Chinese primary, English secondary)
- Images stored in `/public/images/` organized by feature/provider
- MDX frontmatter includes title, icon, and description
- Navigation structure controlled by `meta.json` files

### Analytics
- Amplitude integration for user tracking
- Configured via environment variables

## Key Technical Details

### Fumadocs Integration
- Uses Fumadocs for documentation generation
- Source loader configured in `lib/source.ts`
- Supports Lucide React icons in navigation
- Base URL for docs: `/docs`

### Styling Approach
- Tailwind CSS for utility-first styling
- Global styles in `app/global.css`
- Component-level styling patterns
- Responsive design considerations

### Static Assets
Comprehensive image organization in `/public/images/`:
- Provider logos and screenshots organized by service
- Feature demonstration images
- Authentication flow illustrations