---
title: Overview
icon: House
description: HiveChat is an AI chat application designed for small and medium teams, supporting multi-user mode, permission management, MCP, and integration with various models including Deepseek, OpenAI, Claude, and Gemini.
---

## Feature Overview
One administrator configuration, easy access to various AI models for the entire team.

![image](/images/index/01.png)

* Support for email login or enterprise WeChat, DingTalk, and Feishu login
* Support for group-based user management
    * Set different available models for grouped users
    * Set monthly token limits for different user groups
* Support for MCP server configuration
* Chain of thought display
* LaTeX and Markdown rendering
* Image understanding
* AI agents
* Cloud data storage
* Supported AI model providers:
    * OpenAI
    * Claude
    * Gemini
    * DeepSeek
    * Moonshot
    * Volcano Engine (Doubao)
    * Alibaba Bailian (Qwen)
    * Baidu Qianfan
    * Tencent Hunyuan
    * Zhipu
    * OpenRouter
    * Grok
    * Ollama
    * SiliconFlow
 * Also supports custom addition of any OpenAI-style providers

### User Interface
Login to your account and start chatting.
![image](/images/index/01.png)

MCP Usage

![image](/images/index/02.png)

### Admin Dashboard

* Administrator configures AI model providers
* Can manually add users or enable/disable account registration, suitable for small teams in companies/schools/organizations
* View and manage all users

![image](/images/index/03.png)

User management: set user groups and configure visible models and token limits for different groups
<img src="/images/index/04.png" />
<img src="/images/index/05.png" />
Email and third-party login
<img src="/images/index/06.png" />
MCP configuration
<img src="/images/index/07.png" />


## Online Demo

Note: The following are demo sites, data may be cleared at any time

* User Interface: https://chat.yotuku.cn/
    * You can register an account to experience
* Admin Interface: https://hivechat-demo.vercel.app/
    * Email: <EMAIL>
    * Password: helloHivechat

## Tech Stack

* Next.js
* Tailwindcss
* Auth.js
* PostgreSQL
* Drizzle ORM
* Ant Design