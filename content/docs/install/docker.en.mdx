---
title: Docker
---

1. Clone this project to local
```
git clone https://github.com/HiveNexus/hivechat.git
```

2. Modify local configuration file

Copy the example file to `.env`
```shell
cp .env.example .env
```
Set the following configuration items according to actual needs. `AUTH_SECRET` and `ADMIN_CODE` must be reset in production environment, but can be left unchanged for testing purposes.

`.env` file description:

```
# PostgreSQL database connection URL, can be left empty for Docker deployment
DATABASE_URL=

# Used for encrypting sensitive information such as user data. You can use openssl rand -base64 32 to generate a random 32-character string as the key. This is an example, please replace with your own generated value. Can be left unchanged for testing purposes.
AUTH_SECRET=hclqD3nBpMphLevxGWsUnGU6BaEa2TjrCQ77weOVpPg=

# Administrator authorization code. After initialization, use this value to set up the administrator account. This is an example, please replace with your own generated value.
ADMIN_CODE=********

# Set to the official domain in production environment, used for callbacks when enabling third-party logins like <PERSON>ish<PERSON>H_URL=http://127.0.0.1:3000

# Whether to enable email login, set to ON to enable, OFF to disable, defaults to enabled if not set
EMAIL_AUTH_STATUS=ON

# Whether to enable Feishu login, set to ON to enable, OFF to disable
FEISHU_AUTH_STATUS=OFF
FEISHU_CLIENT_ID="cli_xxxxxxxxxxxxxxxx"
FEISHU_CLIENT_SECRET="xxxxxxxxHOEWIoE7eDc1Lhc0042OXXXX"

# Whether to enable WeChat Work login, set to ON to enable, OFF to disable
WECOM_AUTH_STATUS=OFF
WECOM_CLIENT_ID="ww728c371c2fXXXXXX"
WECOM_AGENT_ID="100XXXX"
WECOM_CLIENT_SECRET="H-7J4jzG0m1axpXLGshaCDlMOZxdjvkX6bIVLuXXXXXX"

# Whether to enable DingTalk login, set to ON to enable, OFF to disable
DINGDING_AUTH_STATUS=OFF
DINGDING_CLIENT_ID="dingpcfi2kpuplXXXXXX"
DINGDING_CLIENT_SECRET="3vk9-VFCExNckqNUk_CL2F-HEgz7qGN-BimH0lZ1gUx6hWO7g_an2lnkk6XXXXXX"
```

3. Start containers
```
docker compose up -d
```

4. Initialize administrator account


Visit `http://localhost:3000/setup` (actual domain and port number), you can enter the administrator account setup page. After setup is complete, you can use the system normally.