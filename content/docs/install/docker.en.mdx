---
title: Docker 部署
---

1. 克隆本项目到本地
```
git clone https://github.com/HiveNexus/hivechat.git
```

2. 修改本地配置文件

将样例文件复制到 `.env`
```shell
cp .env.example .env
```
根据实际需求情况设置如下的配置项，其中 `AUTH_SECRET` 和 `ADMIN_CODE` 在正式环境务必重新设置，测试用途时可不修改。

`.env` 文件说明如下：

```
# PostgreSQL 数据库连接 URL，Docker 部署时可留空
DATABASE_URL=

#用于用户信息等敏感信息的加密，可以使用 openssl rand -base64 32 生成一个随机的 32 位字符串作为密钥，此处为示例，请替换为自己生成的值，测试用途时可不修改。
AUTH_SECRET=hclqD3nBpMphLevxGWsUnGU6BaEa2TjrCQ77weOVpPg=

# 管理员授权码，初始化后，凭此值设置管理员账号，此处为示例，请替换为自己生成的值。
ADMIN_CODE=22113344

# 生产环境设置为正式域名，开启飞书等第三方登录时回调时会使用
NEXTAUTH_URL=http://127.0.0.1:3000

# 是否开启邮箱登录，开启值设为 ON，关闭时修改为 OFF，未设置时默认开启
EMAIL_AUTH_STATUS=ON

# 是否开启飞书登录，开启值设为 ON，关闭时修改为 OFF
FEISHU_AUTH_STATUS=OFF
FEISHU_CLIENT_ID="cli_xxxxxxxxxxxxxxxx"
FEISHU_CLIENT_SECRET="xxxxxxxxHOEWIoE7eDc1Lhc0042OXXXX"

# 是否开启企业微信登录，开启值设为 ON，关闭时修改为 OFF
WECOM_AUTH_STATUS=OFF
WECOM_CLIENT_ID="ww728c371c2fXXXXXX"
WECOM_AGENT_ID="100XXXX"
WECOM_CLIENT_SECRET="H-7J4jzG0m1axpXLGshaCDlMOZxdjvkX6bIVLuXXXXXX"

# 是否开启钉钉登录，开启值设为 ON，关闭时修改为 OFF
DINGDING_AUTH_STATUS=OFF
DINGDING_CLIENT_ID="dingpcfi2kpuplXXXXXX"
DINGDING_CLIENT_SECRET="3vk9-VFCExNckqNUk_CL2F-HEgz7qGN-BimH0lZ1gUx6hWO7g_an2lnkk6XXXXXX"
```

3. 启动容器
```   
docker compose up -d
```

4. 初始化管理员账号
   

访问 `http://localhost:3000/setup` (实际使用的域名和端口号)，即可进入管理员账号设置页面，设置完成后，即可正常使用系统。