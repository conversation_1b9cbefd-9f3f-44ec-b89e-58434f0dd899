---
title: Local
---

1. <PERSON>lone project code to local
```
git clone https://github.com/HiveNexus/hivechat.git
```

2. Install dependencies

```shell
cd hivechat
npm install
```

3. Modify local configuration file

Copy the example file to `.env`
```shell
cp .env.example .env
```

Modify the .env file

```
# PostgreSQL database connection URL, this is an example, requires local installation or connection to remote PostgreSQL
# Note: Local installation currently does not support using Serverless PostgreSQL provided by Vercel or Neon
DATABASE_URL=postgres://postgres:password@localhost/hivechat

# Used for encrypting sensitive information such as user data. You can use openssl rand -base64 32 to generate a random 32-character string as the key. This is an example, please replace with your own generated value.
AUTH_SECRET=hclqD3nBpMphLevxGWsUnGU6BaEa2TjrCQ77weOVpPg=

# Administrator authorization code. After initialization, use this value to set up the administrator account. This is an example, please replace with your own generated value.
ADMIN_CODE=********

# Set to the official domain in production environment, used for callbacks when enabling third-party logins like Feishu
NEXTAUTH_URL=http://127.0.0.1:3000

# Whether to enable email login, set to ON to enable, OFF to disable, defaults to enabled if not set
EMAIL_AUTH_STATUS=ON

# Whether to enable Feishu login, set to ON to enable, OFF to disable
FEISHU_AUTH_STATUS=OFF
FEISHU_CLIENT_ID="cli_xxxxxxxxxxxxxxxx"
FEISHU_CLIENT_SECRET="xxxxxxxxHOEWIoE7eDc1Lhc0042OXXXX"

# Whether to enable WeChat Work login, set to ON to enable, OFF to disable
WECOM_AUTH_STATUS=OFF
WECOM_CLIENT_ID="ww728c371c2fXXXXXX"
WECOM_AGENT_ID="100XXXX"
WECOM_CLIENT_SECRET="H-7J4jzG0m1axpXLGshaCDlMOZxdjvkX6bIVLuXXXXXX"

# Whether to enable DingTalk login, set to ON to enable, OFF to disable
DINGDING_AUTH_STATUS=OFF
DINGDING_CLIENT_ID="dingpcfi2kpuplXXXXXX"
DINGDING_CLIENT_SECRET="3vk9-VFCExNckqNUk_CL2F-HEgz7qGN-BimH0lZ1gUx6hWO7g_an2lnkk6XXXXXX"
```

4. Initialize database
```shell
npm run initdb
```
5. Start the application

```
// Development testing
npm run dev
// Production start
npm run build
npm run start
```
6. Initialize administrator account

Visit `http://localhost:3000/setup` (actual domain and port number), you can enter the administrator account setup page. After setup is complete, you can use the system normally.